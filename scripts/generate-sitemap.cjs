const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
const dotenv = require('dotenv');

dotenv.config({ path: '.env.local' });

const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  charset: 'utf8mb4',
  timezone: '+00:00',
  ssl: false
};

if (!process.env.DB_HOST || !process.env.DB_USER || !process.env.DB_NAME) {
  console.error('❌ متغيرات قاعدة البيانات غير محملة من .env.local');
  console.error('تأكد من وجود الملف .env.local مع المتغيرات التالية:');
  console.error('DB_HOST, DB_USER, DB_PASSWORD, DB_NAME');
  process.exit(1);
}

const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || process.env.NEXT_PUBLIC_APP_URL || 'https://droobhajer.com';
const siteUrlWithWWW = siteUrl.replace('https://droobhajer.com', 'https://www.droobhajer.com');

/**
 * إنشاء ملف sitemap.xml محسن مع XML Schema validation و hreflang للغات المتعددة
 * @param {Array} urls - مصفوفة URLs مع معلومات كل صفحة
 * @returns {string} محتوى XML للـ sitemap
 */
function generateSitemapXML(urls) {
  // تجميع URLs حسب النوع والمعرف لإنشاء hreflang
  const urlGroups = {};

  urls.forEach(url => {
    // استخراج النوع والمعرف من URL
    const urlParts = url.loc.replace(siteUrl, '').split('/');
    let groupKey = '';

    if (urlParts.length === 2 && urlParts[1] === '') {
      // الصفحة الرئيسية
      groupKey = 'home';
    } else if (urlParts.length === 2) {
      // صفحات اللغة الرئيسية (/ar, /en)
      groupKey = 'home';
    } else if (urlParts.length === 3) {
      // صفحات ثابتة (/ar/about, /en/products)
      groupKey = urlParts[2];
    } else if (urlParts.length === 4) {
      // صفحات ديناميكية (/ar/category/id, /en/subcategory/id)
      groupKey = `${urlParts[2]}-${urlParts[3]}`;
    }

    if (!urlGroups[groupKey]) {
      urlGroups[groupKey] = [];
    }
    urlGroups[groupKey].push(url);
  });

  const urlsXML = urls.map(url => {
    // العثور على المجموعة التي تنتمي إليها هذه الصفحة
    const urlParts = url.loc.replace(siteUrl, '').split('/');
    let groupKey = '';

    if (urlParts.length === 2 && urlParts[1] === '') {
      groupKey = 'home';
    } else if (urlParts.length === 2) {
      groupKey = 'home';
    } else if (urlParts.length === 3) {
      groupKey = urlParts[2];
    } else if (urlParts.length === 4) {
      groupKey = `${urlParts[2]}-${urlParts[3]}`;
    }

    const relatedUrls = urlGroups[groupKey] || [];

    // إنشاء hreflang links
    const hreflangLinks = relatedUrls.map(relatedUrl => {
      const relatedParts = relatedUrl.loc.replace(siteUrl, '').split('/');
      let hreflang = 'x-default';

      if (relatedParts.length >= 2) {
        if (relatedParts[1] === 'ar') hreflang = 'ar';
        else if (relatedParts[1] === 'en') hreflang = 'en';
        else if (relatedParts[1] === '') hreflang = 'x-default';
      }

      return `    <xhtml:link rel="alternate" hreflang="${hreflang}" href="${relatedUrl.loc}" />`;
    }).join('\n');

    return `  <url>
    <loc>${url.loc}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>
${hreflangLinks}
  </url>`;
  }).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:xhtml="http://www.w3.org/1999/xhtml"
        xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
        http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
${urlsXML}
</urlset>`;
}

/**
 * إنشاء ملف sitemap-index.xml للفهرسة المحسنة
 * @param {string} lastmod - تاريخ آخر تحديث
 * @returns {string} محتوى XML للـ sitemap index
 */
function generateSitemapIndex(lastmod) {
  return `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
              http://www.sitemaps.org/schemas/sitemap/0.9/siteindex.xsd">
  <sitemap>
    <loc>${siteUrl}/sitemap.xml</loc>
    <lastmod>${lastmod}</lastmod>
  </sitemap>
  <sitemap>
    <loc>${siteUrl}/sitemap-www.xml</loc>
    <lastmod>${lastmod}</lastmod>
  </sitemap>
</sitemapindex>`;
}

/**
 * إنشاء ملف robots.txt محسن
 * @returns {string} محتوى ملف robots.txt
 */
function generateRobotsTxt() {
  return `User-agent: *
Allow: /

# Allow all public pages and content
Allow: /ar/
Allow: /en/
Allow: /ar/*
Allow: /en/*

# Allow static assets and icons
Allow: /favicon.ico
Allow: /favicon-*.png
Allow: /apple-icon-*.png
Allow: /android-icon-*.png
Allow: /ms-icon-*.png
Allow: /manifest.json
Allow: /browserconfig.xml
Allow: /sitemap.xml
Allow: /robots.txt

# Allow specific public pages
Allow: /ar/about
Allow: /en/about
Allow: /ar/contact
Allow: /en/contact
Allow: /ar/products
Allow: /en/products
Allow: /ar/categories
Allow: /en/categories
Allow: /ar/category/*
Allow: /en/category/*
Allow: /ar/subcategory/*
Allow: /en/subcategory/*
Allow: /ar/product/*
Allow: /en/product/*

# Allow additional public pages (if they exist)
Allow: /ar/faq
Allow: /en/faq
Allow: /ar/privacy
Allow: /en/privacy
Allow: /ar/terms
Allow: /en/terms

# Disallow admin and private areas
Disallow: /admin/
Disallow: /api/
Disallow: /_next/
Disallow: /static/

# Disallow user-specific pages
Disallow: /ar/cart
Disallow: /en/cart
Disallow: /ar/checkout
Disallow: /en/checkout
Disallow: /ar/profile
Disallow: /en/profile
Disallow: /ar/dashboard
Disallow: /en/dashboard
Disallow: /ar/login
Disallow: /en/login
Disallow: /ar/register
Disallow: /en/register

# Disallow test and development pages
Disallow: /ar/test-sessions
Disallow: /en/test-sessions
Disallow: /favicon-complete-test.html

# Sitemaps للدومين الرئيسي
Sitemap: ${siteUrl}/sitemap.xml
Sitemap: ${siteUrl}/sitemap-index.xml

# Sitemap للـ www subdomain
Sitemap: ${siteUrl}/sitemap-www.xml`;
}

async function testDatabaseConnection() {
  const configs = [
    dbConfig,
    { ...dbConfig, password: undefined },
    { ...dbConfig, socketPath: '/var/run/mysqld/mysqld.sock', host: undefined }
  ];

  for (let i = 0; i < configs.length; i++) {
    try {
      console.log(`🔄 Trying database connection method ${i + 1}...`);
      const connection = await mysql.createConnection(configs[i]);
      await connection.ping();
      console.log(`✅ Database connection successful with method ${i + 1}`);
      return { connection, config: configs[i] };
    } catch (error) {
      console.log(`❌ Connection method ${i + 1} failed:`, error.message);
      if (i === configs.length - 1) {
        throw error;
      }
    }
  }
}

async function generateSitemap() {
  let connection;

  try {
    console.log('🔄 Testing database connection...');
    console.log('📊 Database config:', {
      host: dbConfig.host,
      port: dbConfig.port,
      user: dbConfig.user,
      database: dbConfig.database,
      hasPassword: !!dbConfig.password
    });

    const { connection: dbConnection } = await testDatabaseConnection();
    connection = dbConnection;
    console.log('✅ Connected to database successfully');

    const currentTime = new Date().toISOString();
    const urls = [];

    const staticPages = [
      // الصفحة الرئيسية - أهم صفحة
      { loc: `${siteUrl}/`, changefreq: 'daily', priority: '1.0' },

      // الصفحات الرئيسية للغات
      { loc: `${siteUrl}/ar`, changefreq: 'daily', priority: '1.0' },
      { loc: `${siteUrl}/en`, changefreq: 'daily', priority: '1.0' },

      // صفحات المنتجات
      { loc: `${siteUrl}/ar/products`, changefreq: 'daily', priority: '0.9' },
      { loc: `${siteUrl}/en/products`, changefreq: 'daily', priority: '0.9' },

      // صفحات الفئات
      { loc: `${siteUrl}/ar/categories`, changefreq: 'weekly', priority: '0.8' },
      { loc: `${siteUrl}/en/categories`, changefreq: 'weekly', priority: '0.8' },

      // صفحات إضافية
      { loc: `${siteUrl}/ar/about`, changefreq: 'monthly', priority: '0.6' },
      { loc: `${siteUrl}/en/about`, changefreq: 'monthly', priority: '0.6' },
      { loc: `${siteUrl}/ar/contact`, changefreq: 'monthly', priority: '0.6' },
      { loc: `${siteUrl}/en/contact`, changefreq: 'monthly', priority: '0.6' }
    ];

    staticPages.forEach(page => {
      urls.push({ ...page, lastmod: currentTime });
    });

    // المنتجات
    console.log('🔄 Fetching products...');
    try {
      const [products] = await connection.execute(
        'SELECT id, updated_at FROM products WHERE deleted_at IS NULL AND is_active = 1 ORDER BY id'
      );
      console.log(`✅ Found ${products.length} products`);
      products.forEach(product => {
        const lastmod = product.updated_at ? new Date(product.updated_at).toISOString() : currentTime;

        // إنشاء slug من عنوان المنتج
        const generateSlug = (text) => {
          if (!text) return '';
          return text
            .toLowerCase()
            .replace(/[^\w\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF-]/g, '')
            .replace(/\s+/g, ' ')
            .trim()
            .replace(/\s/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-+|-+$/g, '');
        };

        const arSlug = generateSlug(product.title_ar);
        const enSlug = generateSlug(product.title);

        urls.push({
          loc: `${siteUrl}/ar/products/${arSlug}-${product.id}`,
          changefreq: 'weekly',
          priority: '0.8',
          lastmod
        });
        urls.push({
          loc: `${siteUrl}/en/products/${enSlug}-${product.id}`,
          changefreq: 'weekly',
          priority: '0.8',
          lastmod
        });
      });
    } catch (e) {
      console.warn('⚠️ Could not fetch products:', e.message);
    }

    // الفئات
    console.log('🔄 Fetching categories...');
    try {
      const [categories] = await connection.execute(
        'SELECT id, updated_at FROM categories WHERE deleted_at IS NULL AND is_active = 1 ORDER BY id'
      );
      console.log(`✅ Found ${categories.length} categories`);
      categories.forEach(category => {
        const lastmod = category.updated_at ? new Date(category.updated_at).toISOString() : currentTime;
        urls.push({
          loc: `${siteUrl}/ar/category/${category.id}`,
          changefreq: 'weekly',
          priority: '0.7',
          lastmod
        });
        urls.push({
          loc: `${siteUrl}/en/category/${category.id}`,
          changefreq: 'weekly',
          priority: '0.7',
          lastmod
        });
      });
    } catch (e) {
      console.warn('⚠️ Could not fetch categories:', e.message);
    }

    // الفئات الفرعية
    console.log('🔄 Fetching subcategories...');
    try {
      const [subcategories] = await connection.execute(
        'SELECT id, updated_at FROM subcategories WHERE deleted_at IS NULL AND is_active = 1 ORDER BY id'
      );
      console.log(`✅ Found ${subcategories.length} subcategories`);
      subcategories.forEach(subcategory => {
        const lastmod = subcategory.updated_at ? new Date(subcategory.updated_at).toISOString() : currentTime;
        urls.push({
          loc: `${siteUrl}/ar/subcategory/${subcategory.id}`,
          changefreq: 'weekly',
          priority: '0.6',
          lastmod
        });
        urls.push({
          loc: `${siteUrl}/en/subcategory/${subcategory.id}`,
          changefreq: 'weekly',
          priority: '0.6',
          lastmod
        });
      });
    } catch (e) {
      console.warn('⚠️ Could not fetch subcategories:', e.message);
    }

    // حفظ الملفات
    const sitemapXML = generateSitemapXML(urls);
    fs.writeFileSync(path.join(process.cwd(), 'public', 'sitemap.xml'), sitemapXML);
    console.log(`✅ Generated sitemap.xml with ${urls.length} URLs`);

    // إنشاء sitemap للـ www (لحل مشكلة Google Search Console)
    const urlsWithWWW = urls.map(url => ({
      ...url,
      loc: url.loc.replace('https://droobhajer.com', 'https://www.droobhajer.com')
    }));
    const sitemapXMLWithWWW = generateSitemapXML(urlsWithWWW);
    fs.writeFileSync(path.join(process.cwd(), 'public', 'sitemap-www.xml'), sitemapXMLWithWWW);
    console.log(`✅ Generated sitemap-www.xml with ${urlsWithWWW.length} URLs for www subdomain`);

    // إنشاء sitemap index محدث
    const sitemapIndex = generateSitemapIndex(currentTime);
    fs.writeFileSync(path.join(process.cwd(), 'public', 'sitemap-index.xml'), sitemapIndex);
    console.log('✅ Generated sitemap-index.xml');

    const robotsTxt = generateRobotsTxt();
    fs.writeFileSync(path.join(process.cwd(), 'public', 'robots.txt'), robotsTxt);
    console.log('✅ Generated robots.txt');
    console.log('🎉 Sitemap generation completed successfully!');

  } catch (error) {
    console.error('❌ Error generating sitemap:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// تشغيل مباشر عند تنفيذ الملف
if (require.main === module) {
  generateSitemap();
}

module.exports = { generateSitemap };
