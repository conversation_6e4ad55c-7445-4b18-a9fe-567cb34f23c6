// اختبار دوال generateSlug

function generateSlug(text) {
  if (!text) return '';
  
  return text
    // تحويل إلى أحرف صغيرة
    .toLowerCase()
    // إزالة الرموز الخاصة والاحتفاظ بالأحرف والأرقام والفراغات والشرطات
    .replace(/[^\w\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF-]/g, '')
    // استبدال الفراغات المتعددة بفراغ واحد
    .replace(/\s+/g, ' ')
    // إزالة الفراغات من البداية والنهاية
    .trim()
    // استبدال الفراغات بشرطات
    .replace(/\s/g, '-')
    // إزالة الشرطات المتعددة
    .replace(/-+/g, '-')
    // إزالة الشرطات من البداية والنهاية
    .replace(/^-+|-+$/g, '');
}

function generateProductUrl(product, locale) {
  const title = locale === 'ar' ? product.title_ar : product.title;
  const slug = generateSlug(title);
  return `/products/${slug}-${product.id}`;
}

function extractProductIdFromSlug(slugWithId) {
  if (!slugWithId) return null;
  
  // البحث عن آخر شرطة في النص
  const lastDashIndex = slugWithId.lastIndexOf('-');
  
  if (lastDashIndex === -1) {
    // إذا لم توجد شرطة، قد يكون المعرف فقط
    return slugWithId;
  }
  
  // استخراج الجزء بعد آخر شرطة
  const potentialId = slugWithId.substring(lastDashIndex + 1);
  
  // التحقق من أن المعرف ليس فارغاً
  if (potentialId.trim() === '') {
    return null;
  }
  
  return potentialId;
}

// اختبار الدوال
console.log('Testing generateSlug:');
console.log('Arabic:', generateSlug('طاجين سيراميك مغربي برتقالي'));
console.log('English:', generateSlug('Tajin Ceramic Moroccan Orange'));
console.log('Mixed:', generateSlug('Tajin طاجين Ceramic سيراميك'));
console.log('Special chars:', generateSlug('Product Name! @#$%^&*()'));

console.log('\nTesting extractProductIdFromSlug:');
console.log('Extract from tajin-ceramic-moroccan-orange-RH02455:', extractProductIdFromSlug('tajin-ceramic-moroccan-orange-RH02455'));
console.log('Extract from product-123:', extractProductIdFromSlug('product-123'));
console.log('Extract from simple-id:', extractProductIdFromSlug('simple-id'));

console.log('\nTesting generateProductUrl:');
const testProduct = { 
  id: 'RH02455', 
  title: 'Tajin Ceramic Moroccan Orange', 
  title_ar: 'طاجين سيراميك مغربي برتقالي' 
};
console.log('Arabic URL:', generateProductUrl(testProduct, 'ar'));
console.log('English URL:', generateProductUrl(testProduct, 'en'));

const testProduct2 = { 
  id: 'ABC123', 
  title: 'Buffet Plate Set', 
  title_ar: 'طقم أطباق بوفيه' 
};
console.log('Arabic URL 2:', generateProductUrl(testProduct2, 'ar'));
console.log('English URL 2:', generateProductUrl(testProduct2, 'en'));
