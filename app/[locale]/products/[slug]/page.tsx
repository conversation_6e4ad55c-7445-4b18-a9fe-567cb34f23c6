import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { Locale } from '../../../../lib/i18n';
import { ProductWithDetails, Category, Subcategory } from '../../../../types/mysql-database';
import ResponsiveProductDetailPage from '../../../../components/ResponsiveProductDetailPage';
import { extractProductIdFromSlug, isValidProductSlug } from '../../../../utils/generateSlug';

interface ProductPageProps {
  params: Promise<{
    locale: string;
    slug: string;
  }>;
}

// دالة لجلب بيانات المنتج من الخادم
async function fetchProductData(productId: string): Promise<{
  product: ProductWithDetails | null;
  category: Category | null;
  subcategory: Subcategory | null;
}> {
  try {
    console.log('🚀 Server: Fetching product details for ID:', productId);

    // جلب جميع البيانات في طلب واحد محسن
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/products/${productId}`, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'NextJS-Server/1.0',
      },
      next: { revalidate: 300 }, // cache لمدة 5 دقائق
    });

    if (!response.ok) {
      console.error(`❌ Server: Product API returned ${response.status}`);
      return { product: null, category: null, subcategory: null };
    }

    const result = await response.json();
    console.log('📦 Server: Product API response received');

    if (result.success && result.data) {
      const { product, category, subcategory } = result.data;
      return { product, category, subcategory };
    } else {
      console.error('❌ Server: Invalid product data structure:', result);
      return { product: null, category: null, subcategory: null };
    }
  } catch (error) {
    console.error('❌ Server: Error fetching product details:', error);
    return { product: null, category: null, subcategory: null };
  }
}

export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const locale = (resolvedParams?.locale || 'ar') as Locale;
  const slug = resolvedParams?.slug || '';

  // استخراج معرف المنتج من الـ slug
  const productId = extractProductIdFromSlug(slug);
  
  if (!productId) {
    return {
      title: 'منتج غير موجود - DROOB HAJER',
      description: 'المنتج المطلوب غير موجود',
    };
  }

  // جلب بيانات المنتج
  const { product } = await fetchProductData(productId);

  if (!product) {
    return {
      title: 'منتج غير موجود - DROOB HAJER',
      description: 'المنتج المطلوب غير موجود',
    };
  }

  const productTitle = locale === 'ar' ? product.title_ar : product.title;
  const productDescription = locale === 'ar' ? product.description_ar : product.description;

  // إنشاء الرابط الكامل للمنتج
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || process.env.NEXT_PUBLIC_APP_URL || 'https://droobhajer.com';
  const productUrl = `${baseUrl}/${locale}/products/${slug}`;

  // تحويل روابط الصور المحلية إلى روابط كاملة
  const productImages = product.images && product.images.length > 0
    ? product.images.map(img => ({
        url: img.image_url.startsWith('http') ? img.image_url : `${baseUrl}${img.image_url}`,
        width: 1200,
        height: 630,
        alt: productTitle,
      }))
    : [{
        url: `${baseUrl}/placeholder-image.jpg`,
        width: 1200,
        height: 630,
        alt: productTitle,
      }];

  return {
    title: `${productTitle} - DROOB HAJER`,
    description: productDescription || `تسوق ${productTitle} من متجر دروب هاجر - أفضل الأسعار وأعلى جودة`,
    keywords: `${productTitle}, دروب هاجر, متجر إلكتروني, ${locale === 'ar' ? 'منتجات عالية الجودة' : 'high quality products'}`,
    openGraph: {
      title: `${productTitle} - DROOB HAJER`,
      description: productDescription || `تسوق ${productTitle} من متجر دروب هاجر`,
      url: productUrl,
      siteName: 'DROOB HAJER',
      images: productImages,
      locale: locale === 'ar' ? 'ar_SA' : 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: `${productTitle} - DROOB HAJER`,
      description: productDescription || `تسوق ${productTitle} من متجر دروب هاجر`,
      images: productImages,
    },
    alternates: {
      canonical: productUrl,
      languages: {
        'ar': `${baseUrl}/ar/products/${slug}`,
        'en': `${baseUrl}/en/products/${slug}`,
      },
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

export default async function ProductDetailPage({ params }: ProductPageProps) {
  const resolvedParams = await params;
  const locale = (resolvedParams?.locale || 'ar') as Locale;
  const slug = resolvedParams?.slug || '';

  // التحقق من صحة تنسيق الـ slug
  if (!isValidProductSlug(slug)) {
    console.error('❌ Invalid product slug format:', slug);
    notFound();
  }

  // استخراج معرف المنتج من الـ slug
  const productId = extractProductIdFromSlug(slug);
  
  if (!productId) {
    console.error('❌ Could not extract product ID from slug:', slug);
    notFound();
  }

  // جلب البيانات من الخادم
  const { product, category } = await fetchProductData(productId);

  // إذا لم يتم العثور على المنتج، إظهار صفحة 404
  if (!product) {
    console.error('❌ Product not found for ID:', productId);
    notFound();
  }

  return (
    <div lang={locale} dir={locale === 'ar' ? 'rtl' : 'ltr'} className={`${locale === 'ar' ? 'rtl font-tajawal' : 'ltr font-inter'} min-h-screen`}>
      <ResponsiveProductDetailPage
        locale={locale}
        initialProduct={product}
        initialCategory={category}
        initialSubcategory={null}
        productId={productId}
      />
    </div>
  );
}
